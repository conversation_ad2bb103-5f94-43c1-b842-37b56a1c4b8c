
import asyncio
import json
import os
from typing import List, Dict, Any, Optional, Sequence

import pandas as pd
import psycopg2
from dotenv import load_dotenv
from duckduckgo_search import DDGS

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.ui import Console
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 加载环境变量
load_dotenv()

# 模型客户端配置#############################################################
def qwen3_client():
    """设置Qwen3模型客户端"""
    model_config = {
        "model": os.getenv("QWEN3_MODEL"),
        "api_key": os.getenv("QWEN3_API_KEY"),
        "base_url": os.getenv("QWEN3_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "multiple_system_messages": True
        },
        "enable_thinking": False, #非思考模式
    }
    return OpenAIChatCompletionClient(**model_config)

def deepseek_client():
    """设置DeepSeek模型客户端"""
    model_config = {
        "model": os.getenv("DEEPSEEK_MODEL"),
        "api_key": os.getenv("DEEPSEEK_API_KEY"),
        "base_url": os.getenv("DEEPSEEK_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "multiple_system_messages": True,
        },
    }
    return OpenAIChatCompletionClient(**model_config)

# 数据库连接工具#########################################################
def connect_to_database():
    """连接到PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            database=os.getenv("DB_NAME")
        )
        return conn
    except Exception as e:
        return f"数据库连接错误: {str(e)}"

def get_database_schema() -> dict:
    """获取数据库表结构信息"""
    conn = connect_to_database()
    if isinstance(conn, str):
        return conn  # 返回错误信息
    
    try:
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        tables = cursor.fetchall()
        
        schema_info = {}
        
        # 获取每个表的列信息
        for table in tables:
            table_name = table[0]
            cursor.execute(f"""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_schema = 'public' AND table_name = '{table_name}'
            """)
            columns = cursor.fetchall()
            schema_info[table_name] = {col[0]: col[1] for col in columns}
        
        cursor.close()
        conn.close()
        
        return schema_info
    except Exception as e:
        conn.close()
        return f"获取数据库结构错误: {str(e)}"

# 执行SQL查询工具
def execute_sql_query(sql_query: str) -> dict:
    """执行SQL查询并返回结果"""
    conn = connect_to_database()
    if isinstance(conn, str):
        return conn  # 返回错误信息
    
    try:
        # 使用pandas读取SQL查询结果
        df = pd.read_sql_query(sql_query, conn)
        conn.close()
        
        # 将DataFrame转换为字典列表
        result = df.to_dict(orient='records')
        return {
            "success": True,
            "result": result,
            "columns": df.columns.tolist(),
            "row_count": len(result)
        }
    except Exception as e:
        conn.close()
        return {
            "success": False,
            "error": str(e)
        }

# 网络搜索工具
def web_search(query: str) -> str:
    """从互联网搜索信息"""
    try:
        with DDGS() as ddgs:
            results = list(ddgs.text(query, max_results=5))
        
        if not results:
            return "未找到相关信息。"
        
        # 提取搜索结果的摘要
        summaries = [f"- {result['title']}: {result['body']}" for result in results]
        return "\n\n".join(summaries)
    except Exception as e:
        return f"搜索过程中出现错误: {str(e)}"


# 创建智能体###############################################
# 1. 任务规划专家
planning_agent = AssistantAgent(
    name="planning_expert",
    model_client=deepseek_client(),
    model_client_stream=True,
    description="An agent for planning tasks, this agent should be the first to engage when given a new task",
    system_message="""你是一位任务规划专家，负责将复杂的美妆销售数据分析任务拆解为更小、可管理的子任务。
你需要根据用户的问题，决定需要调用哪些专家来协作完成任务。

你的团队成员包括：
1. query_analysis_expert（数据分析问题解析专家）：分析用户查询并结合数据库结构，为SQL生成提供详细分析
2. sql_generator_expert（SQL语句生成专家）：将用户查询转换为精确的SQL语句
3. sql_executor_expert（SQL语句执行专家）：连接数据库并执行SQL查询
4. visualization_expert（数据可视化专家）：推荐合适的数据可视化方式
5. data_analysis_expert（数据分析专家）：分析SQL查询结果并回答用户问题
6. knowledge_retrieval_expert（知识检索专家）：从互联网查询美妆行业相关知识

你只需规划和分配任务，不需要亲自执行。

当你分配任务时，请使用以下格式:
    1. <agent> : <task>

在保证准确性的前提下，尽量精简规划，可以回答用户问题即可

""",

)
    
# 2. 数据分析问题解析专家
schema_info="""CREATE TABLE beauty_sales_data (
    sale_id INT PRIMARY KEY,           -- 销售ID，主键
    product_id INT,                    -- 产品ID
    product_name VARCHAR(255),         -- 产品名称
    category VARCHAR(100),             -- 产品类别 (例如: 护肤品, 彩妆, 香水, 男士护理)
    brand VARCHAR(100),                -- 品牌
    price DECIMAL(10, 2),              -- 销售价格
    quantity INT,                      -- 销售数量
    sale_date DATE,                    -- 销售日期
    region VARCHAR(50),                -- 销售区域 (例如: 华东, 华南)
    customer_id VARCHAR(50),           -- 客户ID
    customer_gender VARCHAR(10),       -- 客户性别 (例如: 男, 女)
    customer_age_group VARCHAR(20)     -- 客户年龄段 (例如: 18-24, 25-34)
);"""
query_analysis_output_json_template="""
{
"query_intent": "用户查询的主要意图",
"entities": ["查询中涉及的实体列表"],
"required_tables": ["需要查询的表名列表"],
"required_fields": ["需要的字段列表"],
"conditions": ["查询条件列表"],
"joins": ["需要的表连接描述"],
"grouping": "是否需要分组及分组字段",
"ordering": "是否需要排序及排序字段",
"aggregations": ["需要的聚合函数"],
"summary": "对关键步骤和考虑因素的总结"
}
"""
query_analysis_agent = AssistantAgent(
    name="query_analysis_expert",  # 改为英文名称
    model_client=deepseek_client(),
    model_client_stream=True,
    description="深入分析用户的自然语言查询，结合数据库表结构，生成详细的SQL命令分析，描述如何将用户意图转化为SQL查询的关键步骤和考虑因素。",
    system_message=f"""你是一位用户问题解析专家，专门分析用户的自然语言查询，并结合给定的数据库表结构信息，生成详细的SQL命令分析。

你的职责是：
1. 理解用户的查询意图
2. 识别查询中涉及的实体、条件和关系
3. 确定需要查询的表和字段
4. 分析可能需要的表连接和过滤条件
5. 考虑排序、分组和聚合函数的使用

你可以利用工具，查询数据库表结构信息。

下面是创建表的脚本信息：
{schema_info}

你的输出应该是JSON格式，包含以下字段（如果不需要哪个字段可以不写）：
{query_analysis_output_json_template}

请确保你的分析全面且准确，为SQL语句生成专家提供充分的信息。
""",

)

# 3. SQL语句生成专家
sql_generator_agent = AssistantAgent(
    name="sql_generator_expert",  # 改为英文名称
    model_client=deepseek_client(),
    model_client_stream=True,
    system_message="""你是一位SQL语句生成专家，负责通过将数据分析问题解析、拆解专家的分析结果转换为精确的SQL语句。

你需要基于用户问题解析专家提供的分析和数据库结构信息，生成符合PostgreSQL语法的SQL查询语句。

你只需要输出SQL语句，不需要任何解释或说明。以便后续专家的工作
""",
    description="将数据分析问题解析、拆解的结果转换为精确的SQL语句。"
)

# 4. SQL语句执行专家
sql_executor_agent = AssistantAgent(
    name="sql_executor_expert",  # 改为英文名称
    model_client=deepseek_client(),
    model_client_stream=True,
    tools=[execute_sql_query],
    system_message="""你是一位SQL语句执行专家，负责连接数据库并执行SQL查询。

你的职责是：
1. 接收SQL语句生成专家提供的SQL查询
2. 使用execute_sql_query工具执行查询
3. 返回查询结果或错误信息

如果SQL执行成功，请返回查询结果和相关统计信息，你可以以markdown格式或者dataframe方式返回。
如果执行失败，请分析错误原因并提供修正建议。


""",
    description="连接数据库，执行SQL语句，返回查询结果。"
)

# 5. 数据可视化专家
visualization_agent_system_prompt = """
        你是一名专业的数据可视化专家，负责根据提供的用户指令、SQL查询及其结果数据，推荐最合适的数据可视化方式，并给出详细的配置建议。

        ## 规则

        1. **分析SQL查询：** 理解SQL查询的目标，例如是进行趋势分析、比较不同类别的数据、展示数据分布还是显示详细数据。
        2. **分析查询结果数据结构：** 检查返回的数据包含哪些字段，它们的数据类型（数值型、分类型等），以及数据的组织方式（例如，是否包含时间序列、类别标签、数值指标等）。
        3. **基于数据结构和查询目标推荐可视化类型：**
            * 如果数据涉及**时间序列**且需要展示**趋势**，推荐 `"line"` (折线图)。
            * 如果需要**比较不同类别**的**数值大小**，推荐 `"bar"` (柱状图)。
            * 如果需要展示**各部分占总体的比例**，且类别数量不多，推荐 `"pie"` (饼图)。需要确保数值型字段是总量的一部分。
            * 如果需要展示**两个数值变量之间的关系**或**数据点的分布**，推荐 `"scatter"` (散点图)。
            * 如果数据结构复杂、细节重要，或者无法找到合适的图表类型清晰表达，推荐 `"table"` (表格)。
        4. **提供详细的可视化配置建议：** 根据选择的可视化类型，提供具体的配置参数。
            * **通用配置：** `"title"` (图表标题，应简洁明了地概括图表内容)。
            * **柱状图 (`"bar"`):**
                * `"xAxis"` (X轴字段名，通常是分类型字段)。
                * `"yAxis"` (Y轴字段名，通常是数值型字段)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **折线图 (`"line"`):**
                * `"xAxis"` (X轴字段名，通常是时间或有序的分类型字段)。
                * `"yAxis"` (Y轴字段名，通常是数值型字段)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **饼图 (`"pie"`):**
                * `"nameField"` (名称字段名，通常是分类型字段，用于显示饼图的标签)。
                * `"valueField"` (数值字段名，用于计算每个扇区的大小)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **散点图 (`"scatter"`):**
                * `"xAxis"` (X轴字段名，通常是数值型字段)。
                * `"yAxis"` (Y轴字段名，通常是数值型字段)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **表格 (`"table"`):** 不需要特定的坐标轴或系列配置，可以考虑添加 `"columns"` 字段，列出需要在表格中显示的字段名。
        5. **输出格式必须符合如下JSON格式:**

            ```json
            {
                "type": "可视化类型",
                "config": {
                    "title": "图表标题",
                    "xAxis": "X轴字段名",
                    "yAxis": "Y轴字段名",
                    "seriesName": "系列名称"
                    // 其他配置参数根据可视化类型添加
                }
            }
            ```

            对于饼图：

            ```json
            {
                "type": "pie",
                "config": {
                    "title": "图表标题",
                    "nameField": "名称字段名",
                    "valueField": "数值字段名",
                    "seriesName": "系列名称"
                }
            }
            ```

            对于表格：

            ```json
            {
                "type": "table",
                "config": {
                    "title": "数据表格",
                    "columns": ["字段名1", "字段名2", ...]
                }
            }
            ```

        ## 支持的可视化类型

        - `"bar"`: 柱状图
        - `"line"`: 折线图
        - `"pie"`: 饼图
        - `"scatter"`: 散点图
        - `"table"`: 表格(对于不适合图表的数据)
        特别注意：如果用户有对生成的图表有明确的特定要求，一定要严格遵守用户的指令。例如用户明确要求生成饼状图，就不能生成柱状图。

        重要：你的输出必须是一个有效的JSON对象，格式必须严格按照上述示例，不要添加任何额外的字段或解释。
        """
visualization_agent = AssistantAgent(
    name="visualization_expert",  # 改为英文名称
    model_client=deepseek_client(),
    model_client_stream=True,
    system_message=visualization_agent_system_prompt,
    description="根据SQL查询结果推荐最合适的数据可视化方式，并提供详细配置建议。"
)

# 6. 数据分析专家
data_analysis_agent = AssistantAgent(
    name="data_analysis_expert",  # 改为英文名称
    model_client=qwen3_client(),
    model_client_stream=True,
    system_message="""你是一位美妆行业数据分析专家，负责分析SQL查询结果并回答用户的问题。

你的职责是：
1. 理解SQL查询结果
2. 提供对查询结果的详细分析
3. 根据用户的问题，从查询结果中提取相关信息
4. 使用自然语言回答用户的问题


请确保你的分析准确且详细，能够帮助用户更好地理解查询结果(请你注意，我说的“准确且详细”是指，能回答用户问题即可，不要做过多没有依据的拓展)。
""",
    description="分析SQL查询结果并回答用户的问题。"
)

# 7. 知识检索专家
knowledge_retrieval_agent = AssistantAgent(
    name="knowledge_retrieval_expert",  # 改为英文名称
    model_client=qwen3_client(),
    model_client_stream=True,
    tools=[web_search],
    system_message="""你是一位知识检索专家，负责从互联网查询美妆行业相关知识。

你的职责是：
1. 理解用户的问题
2. 使用互联网资源查询工具查询与问题相关的信息
3. 提供查询结果的摘要和相关链接

请确保你的检索结果准确且相关，能够帮助用户获得所需的信息。
""",
    description="从互联网查询美妆行业相关知识。"
)

#************************************************************************************************
#8.所有任务完成终止条件判断人
termivation_decision_agent = AssistantAgent(
    name="termination_decision_expert",  # 改为英文名称
    model_client=deepseek_client(),
    model_client_stream=True,
    system_message="""你是一位团队终止条件判断专家，负责判断团队是否完成所有任务。

你的职责是：
1. 监听团队的聊天记录
2. 判断团队是否完成了planning_expert规划的所有任务
3. 如果完成，输出"TERMINATE"，结束团队的聊天
"""
 )

#************************************************************************************************************

# 终止条件
text_mention_termination = TextMentionTermination("TERMINATE")
max_messages_termination = MaxMessageTermination(max_messages=10)
termination = text_mention_termination | max_messages_termination

selector_prompt="""
Select an agent to perform task.

{roles}

Current conversation context:
{history}

Read the above conversation, then select an agent from {participants} to perform the next task.
Make sure the planner agent has assigned tasks before other agents start working.
Only select one agent.
"""


# 创建团队
# def selector_func(messages: Sequence[BaseAgentEvent | BaseChatMessage]) -> str | None:
#     if messages[-1].source != planning_agent.name:
#         return planning_agent.name
#     return None

team = SelectorGroupChat(
    [planning_agent, query_analysis_agent, sql_generator_agent, sql_executor_agent, visualization_agent, data_analysis_agent, knowledge_retrieval_agent,termivation_decision_agent],
    model_client=qwen3_client(),
    termination_condition=termination,
    selector_prompt=selector_prompt,
    model_client_streaming=True,
    allow_repeated_speaker=False, # Allow an agent to speak multiple turns in a row.
    # selector_func=selector_func,
)
    



if  __name__ == "__main__":
    async def test_run_stream() -> None:
        # # Option 1: read each message from the stream (as shown in the previous example).
        # async for message in team.run_stream(task="保湿精华液的价格是多少？"):
        #     print(message)
        #
        # Option 2: use Console to print all messages as they appear.
        await Console(
            team.run_stream(task="保湿精华液的价格是多少？"),
            output_stats=True,  # Enable stats printing.
        )


    asyncio.run(test_run_stream())
