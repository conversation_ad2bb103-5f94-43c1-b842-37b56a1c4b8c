import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.ui import Console
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

def deepseek_client():
    """设置DeepSeek模型客户端"""
    model_config = {
        "model": os.getenv("DEEPSEEK_MODEL"),
        "api_key": os.getenv("DEEPSEEK_API_KEY"),
        "base_url": os.getenv("DEEPSEEK_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "multiple_system_messages": True,
        },
    }
    return OpenAIChatCompletionClient(**model_config)
model_client = deepseek_client()

info="""CREATE TABLE beauty_sales_data (
    sale_id INT PRIMARY KEY,           -- 销售ID，主键
    product_id INT,                    -- 产品ID
    product_name VARCHAR(255),         -- 产品名称
    category VARCHAR(100),             -- 产品类别 (例如: 护肤品, 彩妆, 香水, 男士护理)
    brand VARCHAR(100),                -- 品牌
    price DECIMAL(10, 2),              -- 销售价格
    quantity INT,                      -- 销售数量
    sale_date DATE,                    -- 销售日期
    region VARCHAR(50),                -- 销售区域 (例如: 华东, 华南)
    customer_id VARCHAR(50),           -- 客户ID
    customer_gender VARCHAR(10),       -- 客户性别 (例如: 男, 女)
    customer_age_group VARCHAR(20)     -- 客户年龄段 (例如: 18-24, 25-34)
);"""
query_analysis_agent = AssistantAgent(
    name="ai助手",
    model_client=model_client,
    description="用户问题解答",
    system_message=f""" 直接告诉用户{info}""",

)
async def test():

    async for message in query_analysis_agent.run_stream(task="info是什么"):
        print(message)

if __name__ == "__main__":
    asyncio.run(test())