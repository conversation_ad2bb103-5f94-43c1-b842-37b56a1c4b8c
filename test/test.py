import asyncio

import psycopg2
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()
def connect_to_database():
    """连接到PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            database=os.getenv("DB_NAME")
        )
        return conn
    except Exception as e:
        return f"数据库连接错误: {str(e)}"


from autogen_core.tools import FunctionTool


# Define a tool using a Python function.
async def web_search_func(query: str) -> str:
    """Find information on the web"""
    return "AutoGen is a programming framework for building multi-agent applications."


# This step is automatically performed inside the AssistantAgent if the tool is a Python function.
# web_search_function_tool = FunctionTool(web_search_func, description="Find information on the web")
# The schema is provided to the model during AssistantAgent's on_messages call.

def deepseek_client():
    """设置DeepSeek模型客户端"""
    model_config = {
        "model": os.getenv("DEEPSEEK_MODEL"),
        "api_key": os.getenv("DEEPSEEK_API_KEY"),
        "base_url": os.getenv("DEEPSEEK_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "multiple_system_messages": True,
        },
    }
    return OpenAIChatCompletionClient(**model_config)
model_client = deepseek_client()

agent_no_parallel_tool_call = AssistantAgent(
    name="assistant",
    model_client=model_client,
    tools=[web_search_func],
    system_message="Use tools to solve tasks.",
)


async def test():
    agent = AssistantAgent(
        name="assistant",
        model_client=model_client,
        tools=[web_search_func],
        system_message="Use tools to solve tasks.",
        model_client_stream=True,
        # reflect_on_tool_use=True,

    )
    result = await agent.run(task="Find information on AutoGen")
    print(result.messages)

if __name__ == "__main__":
    asyncio.run(test())